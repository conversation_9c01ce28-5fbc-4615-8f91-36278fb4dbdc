请基于vue3组合式api+element plus+Tailwind css开发一款诊所HIS系统前端项目。请保证页面美观与友好交互，页面分为4部分，顶部标题栏，包含系统名称、日夜间切换按钮、用户名下拉菜单（点击后展开切换用户、退出）；左侧为多层级导航栏；中间为内容区，点击左侧页面链接会在次以标签页的形式打开，标签栏最左侧有关闭所有非活动页按钮；右侧为AI问答区，最侧边页面中部有“>”按钮方便隐藏/显示AI问答区。以下是一个更完整、更贴近实际业务需求的左侧多层级导航菜单结构设计：
## 📋 一、首页 / 仪表盘
- 概览数据：今日预约、待处理事项、患者流量、收入统计等。
- 消息通知：系统公告、患者提醒、待办任务。
- 快捷入口：快速挂号、新建问诊、查看最近患者等。
---
## 👤 二、患者管理
- 患者档案：新建、查询、编辑患者信息。
- 患者分组：自定义分组，便于科研或随访管理。
- 患者随访：设置随访计划，记录随访结果。
---
## 📞 三、线上问诊
- 视频/图文问诊：与患者进行远程问诊。
- 问诊记录：查看历史问诊内容、处方、诊断。
- 患者留言：接收并回复患者留言或咨询。
- 问诊统计：问诊量、满意度等数据统计。
---
## 🩺 四、诊疗记录
- 电子病历模板：支持自定义模板，提高录入效率。
- 诊断记录：录入主诉、现病史、检查、诊断结果。
- 处方管理：电子处方、药品选择、打印、发药记录。
- 治疗登记：理疗、康复、针灸、推拿等治疗记录。
- 疗效记录：记录每次治疗后患者的改善情况。
---
## 🧪 五、科研管理
- 表单收集：填写、导出科研数据。
- 数据分析：
  - 图表展示：柱状图、饼图、折线图等。
  - 多维分析：按时间、性别、病种、疗效等维度。
  - 数据导出：Excel、CSV格式导出。
- 科研项目管理：支持多个科研项目分类管理。
---
## 💊 六、药品与耗材管理
- 药品目录：药品名称、规格、价格、库存、医保标识。
- 库存管理：出入库记录、库存预警、药品调拨。
- 药品效期提醒：临近过期自动预警。
- 耗材管理：与药品类似，用于管理理疗用品、器械等。
---
## 💰 七、财务管理
- 收费项目：挂号费、诊疗费、药品费、理疗费等。
- 收费记录：查看每一笔收费明细。
- 退费管理：处理退费申请与记录。
- 报表统计：日/周/月财务报表、收入趋势分析。
- 保险理赔：对接医保系统或商业保险系统。
---
## 📅 八、预约与排班
- 预约挂号：患者在线挂号或前台挂号。
- 排班设置：医生出诊时间安排。
- 预约提醒：短信/微信提醒患者就诊时间。
- 排队叫号：候诊队列管理。
---
## ⚙️ 九、系统设置
- 用户管理：添加、编辑用户，设置角色权限。
- 角色权限：管理员、医生、护士、前台等权限管理。
- 数据字典：维护疾病、科室、药品单位等基础数据。
- 系统参数：调整系统通用设置。
- 日志管理：查看操作日志、登录日志。
- 数据备份与恢复：定期备份系统数据。
---
## 📬 十、消息中心
- 系统通知：发布公告、提醒。
- 患者消息：短信、微信、邮件模板管理。
- 内部沟通：医生与护士之间的内部消息系统。
---
## 📚 十一、知识库与学习中心
- 知识库支持：包括公共知识库（常见病治疗指南、专家共识等）、个人知识库，支持将优质病历添加到个人知识库。

## 十二、表单系统
- 新建表单：拖拽式自定义表单
- 表单分类：将表单归类。
- 表单映射：为系统中需要数据录入的地方指定自定义表单。比如患者信息、诊断记录等。